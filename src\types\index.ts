import { OpenAI } from 'openai';

// Core configuration types
export interface AppConfig {
  model: string;
  provider: string;
  approvalMode: ApprovalPolicy;
  providers?: Record<string, ProviderConfig>;
  projectDocPath?: string;
  maxTokens?: number;
  temperature?: number;
  timeout?: number;
  workdir?: string;
  additionalWritableRoots?: string[];
}

export interface ProviderConfig {
  name: string;
  baseURL: string;
  envKey: string;
  models?: string[];
  defaultModel?: string;
}

export type ApprovalPolicy = "suggest" | "auto-edit" | "full-auto";

// Response and conversation types
export interface ResponseItem {
  id: string;
  type: "message" | "function_call" | "function_result" | "error";
  role: "user" | "assistant" | "system" | "tool";
  content: ResponseContent[];
  timestamp: number;
  metadata?: Record<string, any>;
}

export interface ResponseContent {
  type: "text" | "image" | "function_call" | "function_result";
  text?: string;
  image?: ImageData;
  function_call?: FunctionCall;
  function_result?: FunctionResult;
}

export interface ImageData {
  url?: string;
  base64?: string;
  mimeType: string;
  width?: number;
  height?: number;
}

export interface FunctionCall {
  name: string;
  arguments: string;
  id: string;
}

export interface FunctionResult {
  call_id: string;
  result: string;
  success: boolean;
  metadata?: Record<string, any>;
}

// Input types for the response system
export interface ResponseInputItem {
  role: "user" | "assistant" | "system" | "tool";
  content: ResponseContentInput[];
  type: "message" | "function_call" | "function_result";
  id?: string;
  timestamp?: number;
}

export interface ResponseContentInput {
  type: "input_text" | "input_image" | "function_call" | "function_result";
  text?: string;
  image?: ImageData;
  function_call?: FunctionCall;
  function_result?: FunctionResult;
}

// Tool and function types
export interface FunctionTool {
  type: "function";
  function: {
    name: string;
    description: string;
    parameters: {
      type: "object";
      properties: Record<string, any>;
      required: string[];
    };
  };
}

export interface ResponseFunctionToolCall {
  id: string;
  name: string;
  arguments: string;
}

// Execution types
export interface ExecInput {
  command: string[];
  workdir?: string;
  timeout?: number;
  env?: Record<string, string>;
}

export interface ExecResult {
  success: boolean;
  stdout: string;
  stderr: string;
  exitCode: number;
  duration: number;
  command: string[];
  workdir: string;
  error?: string;
}

// Approval system types
export enum ReviewDecision {
  YES = "yes",
  NO_CONTINUE = "no_continue",
  NO_EXIT = "no_exit", 
  ALWAYS = "always",
  EXPLAIN = "explain"
}

export interface ApprovalRequest {
  command: string[];
  workdir: string;
  description?: string;
  riskLevel: "low" | "medium" | "high";
}

export interface ApprovalResponse {
  decision: ReviewDecision;
  remember?: boolean;
  explanation?: string;
}

// UI and overlay types
export type OverlayModeType = "none" | "history" | "sessions" | "model" | "approval" | "help" | "diff";

export interface ModelInfo {
  id: string;
  name: string;
  provider: string;
  contextLength: number;
  capabilities: string[];
  pricing?: {
    input: number;
    output: number;
  };
}

// Event types for streaming responses
export interface ResponseEvent {
  type: string;
  delta?: string;
  content?: string;
  function_call?: Partial<FunctionCall>;
  error?: string;
  metadata?: Record<string, any>;
}

// Session management
export interface Session {
  id: string;
  name: string;
  created: number;
  updated: number;
  items: ResponseItem[];
  config: Partial<AppConfig>;
}

// File system types
export interface FileSystemSuggestion {
  path: string;
  type: "file" | "directory";
  exists: boolean;
  size?: number;
  modified?: number;
}

// Command history
export interface CommandHistoryItem {
  command: string;
  timestamp: number;
  success: boolean;
  workdir: string;
}

// Error types
export class KritrimaError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: Record<string, any>
  ) {
    super(message);
    this.name = 'KritrimaError';
  }
}

export class NetworkError extends KritrimaError {
  constructor(message: string, details?: Record<string, any>) {
    super(message, 'NETWORK_ERROR', details);
  }
}

export class ConfigError extends KritrimaError {
  constructor(message: string, details?: Record<string, any>) {
    super(message, 'CONFIG_ERROR', details);
  }
}

export class SecurityError extends KritrimaError {
  constructor(message: string, details?: Record<string, any>) {
    super(message, 'SECURITY_ERROR', details);
  }
}

export class ValidationError extends KritrimaError {
  constructor(message: string, details?: Record<string, any>) {
    super(message, 'VALIDATION_ERROR', details);
  }
}
