# Kritrima AI CLI

A sophisticated AI-powered command-line interface with multi-provider support and autonomous agent capabilities.

## Features

### 🤖 Multi-Provider AI Support
- **OpenAI**: GPT-4, GPT-4 Turbo, GPT-4o, GPT-3.5 Turbo, o1 models
- **Google Gemini**: Gemini Pro, Gemini Pro Vision
- **Mistral AI**: All Mistral models
- **DeepSeek**: Chat and Coder models
- **xAI**: Grok models
- **Groq**: Fast inference models
- **Ollama**: Local model support
- **OpenRouter**: Access to multiple providers
- **Custom Providers**: Easy to add new providers

### 🔧 Autonomous Agent Capabilities
- **Shell Command Execution**: Run system commands with AI assistance
- **File Operations**: Read, write, and modify files
- **Tool Calling**: Advanced function calling with real-time execution
- **Context Awareness**: Git integration and project documentation analysis
- **Multi-Modal Input**: Text and image support

### 🔒 Security & Sandboxing
- **Multiple Sandbox Options**: Linux Landlock, macOS Seatbelt, basic security
- **Approval Modes**: Suggest, auto-edit, full-auto
- **Command Validation**: Security checks and path validation
- **Safe Execution**: Isolated command execution when possible

### 💬 Advanced Chat Interface
- **Real-time Streaming**: Live AI responses
- **Rich Terminal UI**: Built with React and Ink
- **Command History**: Navigate through previous commands
- **File Suggestions**: Auto-complete file paths with @
- **Slash Commands**: Built-in commands for model switching, help, etc.

## Installation

```bash
# Clone the repository
git clone https://github.com/kritrima-ai/kritrima-ai-cli.git
cd kritrima-ai-cli

# Install dependencies
npm install

# Build the project
npm run build

# Install globally (optional)
npm install -g .
```

## Quick Start

1. **Set up API keys** (choose one or more):
   ```bash
   export OPENAI_API_KEY="your-openai-key"
   export GEMINI_API_KEY="your-gemini-key"
   export MISTRAL_API_KEY="your-mistral-key"
   # ... other providers
   ```

2. **Run the CLI**:
   ```bash
   # If installed globally
   kritrima-ai

   # Or run directly
   npm run dev

   # With initial message
   kritrima-ai "Help me analyze this codebase"
   ```

3. **Start chatting**:
   - Type your message and press Enter
   - Use `/help` for available commands
   - Use `/model` to switch AI providers
   - Reference files with `@filename`

## Configuration

### Config File Locations
- User config: `~/.kritrima-ai/config.json`
- Project config: `./.kritrima-ai/config.json`

### Example Configuration
```json
{
  "model": "gpt-4",
  "provider": "openai",
  "approvalMode": "suggest",
  "maxTokens": 4096,
  "temperature": 0.7,
  "timeout": 30000,
  "workdir": "/path/to/project"
}
```

### Environment Variables
- `KRITRIMA_AI_MODEL`: Default model
- `KRITRIMA_AI_PROVIDER`: Default provider
- `KRITRIMA_AI_APPROVAL_MODE`: Approval mode
- `OPENAI_API_KEY`: OpenAI API key
- `GEMINI_API_KEY`: Google Gemini API key
- And more for each provider...

## Usage Examples

### Basic Chat
```bash
kritrima-ai "Explain how React hooks work"
```

### File Analysis
```bash
kritrima-ai "Analyze the code in @src/app.tsx and suggest improvements"
```

### System Commands
```bash
# The AI can execute commands based on your approval mode
kritrima-ai "Show me the git status and recent commits"
```

### Model Switching
```bash
# Switch to different provider/model
kritrima-ai --provider gemini --model gemini-pro
```

## Commands

### Slash Commands
- `/help` - Show help information
- `/model` - Switch AI model or provider
- `/history` - View conversation history
- `/clear` - Clear current conversation
- `/approval` - Change approval mode
- `/status` - Show current status
- `/exit` - Exit the application

### Keyboard Shortcuts
- `Ctrl+C` - Exit application
- `Ctrl+M` - Open model selection
- `Ctrl+H` - Show help
- `Ctrl+R` - View history
- `Esc` - Close overlays
- `↑↓` - Navigate command history
- `Tab` - Autocomplete commands/files

## Approval Modes

### Suggest Mode (Default)
- Manual approval required for all commands
- Maximum security and control
- Best for production environments

### Auto-Edit Mode
- Auto-approve safe read-only commands
- Ask for approval on file modifications
- Balanced security and convenience

### Full-Auto Mode
- Auto-approve all commands
- Maximum speed and convenience
- Use only in trusted environments

## Security Features

### Sandboxing
- **Linux**: Landlock LSM for kernel-level isolation
- **macOS**: Seatbelt for system-level sandboxing
- **Fallback**: Basic security checks on all platforms

### Command Validation
- Path traversal protection
- Dangerous command detection
- Working directory restrictions
- Environment variable sanitization

## Development

### Project Structure
```
src/
├── cli.tsx                 # CLI entry point
├── app.tsx                 # Main React app
├── types/                  # TypeScript type definitions
├── utils/                  # Utility functions
│   ├── config.ts          # Configuration management
│   ├── providers.ts       # AI provider definitions
│   ├── openai-client.ts   # OpenAI client factory
│   ├── model-utils.ts     # Model management
│   ├── responses.ts       # Response handling
│   └── agent/             # Agent system
│       ├── agent-loop.ts  # Core agent orchestration
│       ├── handle-exec-command.ts # Command execution
│       ├── platform-commands.ts  # Cross-platform commands
│       └── sandbox/       # Sandboxing implementations
└── components/            # React components
    ├── chat/              # Chat interface
    └── overlays/          # UI overlays
```

### Building
```bash
npm run build    # Build TypeScript
npm run dev      # Development mode
npm run test     # Run tests
npm run lint     # Lint code
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see [LICENSE](LICENSE) file for details.

## Support

- 📖 [Documentation](https://github.com/kritrima-ai/kritrima-ai-cli/wiki)
- 🐛 [Issue Tracker](https://github.com/kritrima-ai/kritrima-ai-cli/issues)
- 💬 [Discussions](https://github.com/kritrima-ai/kritrima-ai-cli/discussions)

---

**⚠️ Security Notice**: This tool can execute system commands. Always review the approval mode settings and understand the security implications before use in production environments.
