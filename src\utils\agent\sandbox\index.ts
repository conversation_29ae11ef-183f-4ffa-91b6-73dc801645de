import * as os from 'os';
import { ExecInput, ExecResult, AppConfig } from '../../../types';
import * as rawExec from './raw-exec';

// Platform-specific sandbox implementations will be imported conditionally
let landlockExec: any = null;
let macOSSeatbeltExec: any = null;

// Try to import platform-specific modules
try {
  if (os.platform() === 'linux') {
    landlockExec = require('./landlock');
  }
} catch (error) {
  // Landlock not available
}

try {
  if (os.platform() === 'darwin') {
    macOSSeatbeltExec = require('./macos-seatbelt');
  }
} catch (error) {
  // macOS Seatbelt not available
}

export interface SandboxCapabilities {
  sandboxed: boolean;
  networkRestricted: boolean;
  filesystemRestricted: boolean;
  processRestricted: boolean;
  name: string;
  securityLevel: string;
}

/**
 * Get the best available sandbox implementation for the current platform
 */
export function getBestSandbox(): {
  exec: (input: ExecInput, config: AppConfig, additionalWritableRoots?: ReadonlyArray<string>, signal?: AbortSignal) => Promise<ExecResult>;
  capabilities: SandboxCapabilities;
  available: boolean;
} {
  const platform = os.platform();

  // Try platform-specific sandboxes first
  if (platform === 'linux' && landlockExec?.isAvailable()) {
    return {
      exec: landlockExec.execWithLandlock,
      capabilities: {
        sandboxed: true,
        networkRestricted: true,
        filesystemRestricted: true,
        processRestricted: true,
        name: 'Linux Landlock',
        securityLevel: 'High - Kernel-level sandboxing'
      },
      available: true
    };
  }

  if (platform === 'darwin' && macOSSeatbeltExec?.isAvailable()) {
    return {
      exec: macOSSeatbeltExec.execWithSeatbelt,
      capabilities: {
        sandboxed: true,
        networkRestricted: true,
        filesystemRestricted: true,
        processRestricted: true,
        name: 'macOS Seatbelt',
        securityLevel: 'High - System-level sandboxing'
      },
      available: true
    };
  }

  // Fallback to raw execution with basic security
  return {
    exec: rawExec.execWithBasicSecurity,
    capabilities: {
      sandboxed: false,
      networkRestricted: false,
      filesystemRestricted: false,
      processRestricted: false,
      name: 'Raw Execution',
      securityLevel: 'Basic - Security checks only'
    },
    available: true
  };
}

/**
 * Execute command with the best available sandbox
 */
export async function execSandboxed(
  input: ExecInput,
  config: AppConfig,
  additionalWritableRoots: ReadonlyArray<string> = [],
  signal?: AbortSignal
): Promise<ExecResult> {
  const sandbox = getBestSandbox();
  return sandbox.exec(input, config, additionalWritableRoots, signal);
}

/**
 * Get current sandbox capabilities
 */
export function getSandboxCapabilities(): SandboxCapabilities {
  return getBestSandbox().capabilities;
}

/**
 * Check if any sandbox is available
 */
export function isSandboxAvailable(): boolean {
  return getBestSandbox().available;
}

/**
 * Get all available sandbox implementations
 */
export function getAvailableSandboxes(): Array<{
  name: string;
  platform: string;
  available: boolean;
  capabilities: SandboxCapabilities;
}> {
  const sandboxes = [];

  // Linux Landlock
  sandboxes.push({
    name: 'Linux Landlock',
    platform: 'linux',
    available: os.platform() === 'linux' && landlockExec?.isAvailable(),
    capabilities: {
      sandboxed: true,
      networkRestricted: true,
      filesystemRestricted: true,
      processRestricted: true,
      name: 'Linux Landlock',
      securityLevel: 'High - Kernel-level sandboxing'
    }
  });

  // macOS Seatbelt
  sandboxes.push({
    name: 'macOS Seatbelt',
    platform: 'darwin',
    available: os.platform() === 'darwin' && macOSSeatbeltExec?.isAvailable(),
    capabilities: {
      sandboxed: true,
      networkRestricted: true,
      filesystemRestricted: true,
      processRestricted: true,
      name: 'macOS Seatbelt',
      securityLevel: 'High - System-level sandboxing'
    }
  });

  // Raw execution (always available)
  sandboxes.push({
    name: 'Raw Execution',
    platform: 'all',
    available: true,
    capabilities: {
      sandboxed: false,
      networkRestricted: false,
      filesystemRestricted: false,
      processRestricted: false,
      name: 'Raw Execution',
      securityLevel: 'Basic - Security checks only'
    }
  });

  return sandboxes;
}

/**
 * Test sandbox functionality
 */
export async function testSandbox(config: AppConfig): Promise<{
  success: boolean;
  sandbox: string;
  capabilities: SandboxCapabilities;
  testResults: Array<{ test: string; passed: boolean; details?: string }>;
}> {
  const sandbox = getBestSandbox();
  const testResults = [];

  try {
    // Test 1: Basic command execution
    const echoTest = await sandbox.exec(
      { command: ['echo', 'test'] },
      config
    );
    testResults.push({
      test: 'Basic command execution',
      passed: echoTest.success && echoTest.stdout.includes('test'),
      details: echoTest.success ? 'OK' : echoTest.stderr
    });

    // Test 2: Working directory
    const pwdTest = await sandbox.exec(
      { command: os.platform() === 'win32' ? ['cd'] : ['pwd'] },
      config
    );
    testResults.push({
      test: 'Working directory access',
      passed: pwdTest.success,
      details: pwdTest.success ? 'OK' : pwdTest.stderr
    });

    // Test 3: File listing
    const listTest = await sandbox.exec(
      { command: os.platform() === 'win32' ? ['dir'] : ['ls'] },
      config
    );
    testResults.push({
      test: 'File system access',
      passed: listTest.success,
      details: listTest.success ? 'OK' : listTest.stderr
    });

    const allPassed = testResults.every(result => result.passed);

    return {
      success: allPassed,
      sandbox: sandbox.capabilities.name,
      capabilities: sandbox.capabilities,
      testResults
    };
  } catch (error) {
    testResults.push({
      test: 'Sandbox initialization',
      passed: false,
      details: error instanceof Error ? error.message : 'Unknown error'
    });

    return {
      success: false,
      sandbox: sandbox.capabilities.name,
      capabilities: sandbox.capabilities,
      testResults
    };
  }
}

/**
 * Get sandbox recommendation based on security requirements
 */
export function getSandboxRecommendation(securityLevel: 'low' | 'medium' | 'high'): {
  recommended: string;
  reason: string;
  alternatives: string[];
} {
  const available = getAvailableSandboxes().filter(s => s.available);
  
  switch (securityLevel) {
    case 'high':
      const highSecurity = available.find(s => s.capabilities.sandboxed);
      if (highSecurity) {
        return {
          recommended: highSecurity.name,
          reason: 'Provides kernel/system-level sandboxing for maximum security',
          alternatives: available.filter(s => s.name !== highSecurity.name).map(s => s.name)
        };
      }
      break;
    
    case 'medium':
      // Prefer sandboxed but accept basic security
      const mediumSecurity = available.find(s => s.capabilities.sandboxed) || available[0];
      return {
        recommended: mediumSecurity.name,
        reason: mediumSecurity.capabilities.sandboxed 
          ? 'Provides good security with sandboxing'
          : 'Basic security checks (sandboxing not available)',
        alternatives: available.filter(s => s.name !== mediumSecurity.name).map(s => s.name)
      };
    
    case 'low':
    default:
      return {
        recommended: 'Raw Execution',
        reason: 'Minimal overhead with basic security checks',
        alternatives: available.filter(s => s.name !== 'Raw Execution').map(s => s.name)
      };
  }

  // Fallback
  return {
    recommended: 'Raw Execution',
    reason: 'Only available option',
    alternatives: []
  };
}

// Export individual sandbox modules for direct access if needed
export { rawExec };
export const landlock = landlockExec;
export const macOSSeatbelt = macOSSeatbeltExec;
