{"name": "kritrima-ai", "version": "1.0.0", "description": "Sophisticated AI-powered command-line interface with multi-provider support and autonomous agent capabilities", "main": "dist/cli.js", "bin": {"kritrima-ai": "./bin/kritrima-ai.js"}, "scripts": {"build": "tsc", "dev": "tsx src/cli.tsx", "start": "node dist/cli.js", "test": "jest", "lint": "eslint src/**/*.{ts,tsx}", "format": "prettier --write src/**/*.{ts,tsx}", "prepare": "npm run build"}, "keywords": ["ai", "cli", "assistant", "openai", "agent", "automation", "coding"], "author": "Kritrima AI", "license": "MIT", "dependencies": {"@types/node": "^20.10.0", "openai": "^4.20.0", "react": "^18.2.0", "react-dom": "^18.2.0", "ink": "^4.4.1", "ink-text-input": "^5.0.1", "ink-select-input": "^5.0.0", "ink-spinner": "^5.0.0", "ink-box": "^3.0.0", "chalk": "^5.3.0", "commander": "^11.1.0", "yaml": "^2.3.4", "fs-extra": "^11.2.0", "glob": "^10.3.10", "mime-types": "^2.1.35", "https-proxy-agent": "^7.0.2", "node-fetch": "^3.3.2", "diff": "^5.1.0", "highlight.js": "^11.9.0", "marked": "^9.1.6", "marked-terminal": "^6.2.0", "clipboardy": "^4.0.0", "node-notifier": "^10.0.1", "strip-ansi": "^7.1.0", "ansi-escapes": "^6.2.0", "terminal-kit": "^3.0.1", "execa": "^8.0.1", "which": "^4.0.0", "semver": "^7.5.4"}, "devDependencies": {"typescript": "^5.3.0", "tsx": "^4.6.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/fs-extra": "^11.0.4", "@types/mime-types": "^2.1.4", "@types/diff": "^5.0.8", "@types/which": "^3.0.3", "@types/semver": "^7.5.6", "@types/jest": "^29.5.8", "jest": "^29.7.0", "ts-jest": "^29.1.1", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "prettier": "^3.1.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/kritrima-ai/kritrima-ai-cli.git"}, "bugs": {"url": "https://github.com/kritrima-ai/kritrima-ai-cli/issues"}, "homepage": "https://github.com/kritrima-ai/kritrima-ai-cli#readme"}