import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import { getFileSystemSuggestions, extractFilePaths } from '../../utils/input-utils';

interface TerminalChatInputProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

interface Suggestion {
  text: string;
  type: 'command' | 'file' | 'directory';
  description?: string;
}

const SLASH_COMMANDS = [
  { command: '/help', description: 'Show help information' },
  { command: '/model', description: 'Switch AI model or provider' },
  { command: '/history', description: 'View conversation history' },
  { command: '/clear', description: 'Clear conversation' },
  { command: '/approval', description: 'Change approval mode' },
  { command: '/status', description: 'Show current status' },
  { command: '/exit', description: 'Exit the application' }
];

export function TerminalChatInput({ onSendMessage, disabled = false, placeholder = "Type your message..." }: TerminalChatInputProps) {
  const [input, setInput] = useState('');
  const [cursorPosition, setCursorPosition] = useState(0);
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [selectedSuggestion, setSelectedSuggestion] = useState(0);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [history, setHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  const inputRef = useRef<string>('');
  const originalInputRef = useRef<string>('');

  // Update refs when input changes
  useEffect(() => {
    inputRef.current = input;
  }, [input]);

  // Handle keyboard input
  useInput((inputChar, key) => {
    if (disabled) return;

    // Handle special keys
    if (key.return) {
      handleSubmit();
      return;
    }

    if (key.escape) {
      setShowSuggestions(false);
      setSelectedSuggestion(0);
      return;
    }

    if (key.tab && showSuggestions && suggestions.length > 0) {
      applySuggestion(suggestions[selectedSuggestion]);
      return;
    }

    if (key.upArrow) {
      if (showSuggestions) {
        setSelectedSuggestion(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
      } else {
        navigateHistory('up');
      }
      return;
    }

    if (key.downArrow) {
      if (showSuggestions) {
        setSelectedSuggestion(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
      } else {
        navigateHistory('down');
      }
      return;
    }

    if (key.leftArrow) {
      setCursorPosition(prev => Math.max(0, prev - 1));
      return;
    }

    if (key.rightArrow) {
      setCursorPosition(prev => Math.min(input.length, prev + 1));
      return;
    }

    if (key.backspace) {
      handleBackspace();
      return;
    }

    if (key.delete) {
      handleDelete();
      return;
    }

    if (key.ctrl && inputChar === 'a') {
      setCursorPosition(0);
      return;
    }

    if (key.ctrl && inputChar === 'e') {
      setCursorPosition(input.length);
      return;
    }

    if (key.ctrl && inputChar === 'u') {
      setInput('');
      setCursorPosition(0);
      setShowSuggestions(false);
      return;
    }

    if (key.ctrl && inputChar === 'k') {
      setInput(input.slice(0, cursorPosition));
      setShowSuggestions(false);
      return;
    }

    // Handle regular character input
    if (inputChar && !key.ctrl && !key.meta) {
      handleCharacterInput(inputChar);
    }
  });

  const handleCharacterInput = (char: string) => {
    const newInput = input.slice(0, cursorPosition) + char + input.slice(cursorPosition);
    setInput(newInput);
    setCursorPosition(prev => prev + 1);
    updateSuggestions(newInput);
  };

  const handleBackspace = () => {
    if (cursorPosition > 0) {
      const newInput = input.slice(0, cursorPosition - 1) + input.slice(cursorPosition);
      setInput(newInput);
      setCursorPosition(prev => prev - 1);
      updateSuggestions(newInput);
    }
  };

  const handleDelete = () => {
    if (cursorPosition < input.length) {
      const newInput = input.slice(0, cursorPosition) + input.slice(cursorPosition + 1);
      setInput(newInput);
      updateSuggestions(newInput);
    }
  };

  const handleSubmit = () => {
    if (input.trim()) {
      onSendMessage(input.trim());
      
      // Add to history
      setHistory(prev => {
        const newHistory = [input.trim(), ...prev.filter(item => item !== input.trim())];
        return newHistory.slice(0, 100); // Keep last 100 commands
      });
      
      setInput('');
      setCursorPosition(0);
      setShowSuggestions(false);
      setHistoryIndex(-1);
    }
  };

  const navigateHistory = (direction: 'up' | 'down') => {
    if (history.length === 0) return;

    if (direction === 'up') {
      if (historyIndex === -1) {
        originalInputRef.current = input;
        setHistoryIndex(0);
        setInput(history[0]);
        setCursorPosition(history[0].length);
      } else if (historyIndex < history.length - 1) {
        const newIndex = historyIndex + 1;
        setHistoryIndex(newIndex);
        setInput(history[newIndex]);
        setCursorPosition(history[newIndex].length);
      }
    } else {
      if (historyIndex > 0) {
        const newIndex = historyIndex - 1;
        setHistoryIndex(newIndex);
        setInput(history[newIndex]);
        setCursorPosition(history[newIndex].length);
      } else if (historyIndex === 0) {
        setHistoryIndex(-1);
        setInput(originalInputRef.current);
        setCursorPosition(originalInputRef.current.length);
      }
    }
  };

  const updateSuggestions = useCallback((text: string) => {
    const newSuggestions: Suggestion[] = [];

    // Slash command suggestions
    if (text.startsWith('/')) {
      const commandPrefix = text.slice(1).toLowerCase();
      for (const cmd of SLASH_COMMANDS) {
        if (cmd.command.slice(1).toLowerCase().startsWith(commandPrefix)) {
          newSuggestions.push({
            text: cmd.command,
            type: 'command',
            description: cmd.description
          });
        }
      }
    }

    // File path suggestions (when @ is used)
    if (text.includes('@')) {
      try {
        const fileSuggestions = getFileSystemSuggestions(text);
        for (const suggestion of fileSuggestions) {
          newSuggestions.push({
            text: `@${suggestion.path}`,
            type: suggestion.type,
            description: suggestion.type === 'directory' ? 'Directory' : 'File'
          });
        }
      } catch (error) {
        // Ignore file system errors
      }
    }

    setSuggestions(newSuggestions);
    setShowSuggestions(newSuggestions.length > 0);
    setSelectedSuggestion(0);
  }, []);

  const applySuggestion = (suggestion: Suggestion) => {
    if (suggestion.type === 'command') {
      setInput(suggestion.text + ' ');
      setCursorPosition(suggestion.text.length + 1);
    } else {
      // For file suggestions, replace the @path part
      const atIndex = input.lastIndexOf('@');
      if (atIndex !== -1) {
        const beforeAt = input.slice(0, atIndex);
        const newInput = beforeAt + suggestion.text + ' ';
        setInput(newInput);
        setCursorPosition(newInput.length);
      }
    }
    
    setShowSuggestions(false);
    setSelectedSuggestion(0);
  };

  // Render input with cursor
  const renderInputWithCursor = () => {
    const beforeCursor = input.slice(0, cursorPosition);
    const atCursor = input[cursorPosition] || ' ';
    const afterCursor = input.slice(cursorPosition + 1);

    return (
      <Text>
        {beforeCursor}
        <Text inverse>{atCursor}</Text>
        {afterCursor}
      </Text>
    );
  };

  return (
    <Box flexDirection="column">
      {/* Suggestions */}
      {showSuggestions && suggestions.length > 0 && (
        <Box flexDirection="column" marginBottom={1} paddingX={1} borderStyle="round">
          <Text color="gray" bold>Suggestions:</Text>
          {suggestions.slice(0, 5).map((suggestion, index) => (
            <Box key={index} paddingLeft={1}>
              <Text color={index === selectedSuggestion ? "cyan" : "gray"}>
                {index === selectedSuggestion ? "▶ " : "  "}
                {suggestion.text}
                {suggestion.description && (
                  <Text color="gray"> - {suggestion.description}</Text>
                )}
              </Text>
            </Box>
          ))}
          {suggestions.length > 5 && (
            <Text color="gray" dimColor>
              ... and {suggestions.length - 5} more (use ↑↓ to navigate)
            </Text>
          )}
        </Box>
      )}

      {/* Input line */}
      <Box>
        <Text color="green" bold>❯ </Text>
        {disabled ? (
          <Text color="gray">{placeholder}</Text>
        ) : input ? (
          renderInputWithCursor()
        ) : (
          <Text color="gray">{placeholder}</Text>
        )}
      </Box>

      {/* Help text */}
      {!disabled && (
        <Box marginTop={1}>
          <Text color="gray" dimColor>
            Press Tab to autocomplete, ↑↓ for history, Ctrl+C to exit
          </Text>
        </Box>
      )}
    </Box>
  );
}
