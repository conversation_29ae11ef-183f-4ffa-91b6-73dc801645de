{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "commonjs", "moduleResolution": "node", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "jsx": "react-jsx", "types": ["node", "jest"], "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/utils/*": ["src/utils/*"], "@/types/*": ["src/types/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx"]}