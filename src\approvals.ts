import { ApprovalPolicy, ReviewDecision, ApprovalRequest, ApprovalResponse, SecurityError } from './types';

/**
 * Safe commands that can be auto-approved in auto-edit mode
 */
const SAFE_COMMANDS = [
  'ls', 'dir', 'cat', 'type', 'head', 'tail', 'grep', 'findstr',
  'pwd', 'cd', 'echo', 'which', 'where', 'file', 'stat',
  'git status', 'git log', 'git diff', 'git branch', 'git show',
  'npm list', 'npm info', 'pip list', 'pip show',
  'node --version', 'npm --version', 'python --version',
  'find', 'locate', 'tree', 'wc', 'sort', 'uniq'
];

/**
 * Dangerous commands that require explicit approval
 */
const DANGEROUS_COMMANDS = [
  'rm', 'del', 'rmdir', 'rd', 'mv', 'move', 'cp', 'copy',
  'chmod', 'chown', 'sudo', 'su', 'passwd',
  'kill', 'killall', 'taskkill', 'pkill',
  'format', 'fdisk', 'mkfs', 'dd',
  'curl', 'wget', 'nc', 'netcat', 'ssh', 'scp', 'rsync',
  'npm install', 'npm uninstall', 'pip install', 'pip uninstall',
  'apt', 'yum', 'brew', 'choco', 'winget',
  'systemctl', 'service', 'crontab', 'at',
  'docker', 'kubectl', 'helm'
];

/**
 * Commands that modify files or system state
 */
const WRITE_COMMANDS = [
  'touch', 'mkdir', 'md', 'echo', 'printf',
  'tee', 'sed', 'awk', 'perl', 'python', 'node',
  'git add', 'git commit', 'git push', 'git pull', 'git merge',
  'npm run', 'npm start', 'npm test', 'npm build'
];

/**
 * Check if a command can be auto-approved based on approval policy
 */
export function canAutoApprove(
  command: string[],
  approvalPolicy: ApprovalPolicy,
  additionalSafeCommands: string[] = []
): boolean {
  if (approvalPolicy === "suggest") {
    return false; // Always require approval in suggest mode
  }

  if (approvalPolicy === "full-auto") {
    return true; // Auto-approve everything in full-auto mode
  }

  // In auto-edit mode, check if command is safe
  const commandStr = command.join(' ').toLowerCase();
  const allSafeCommands = [...SAFE_COMMANDS, ...additionalSafeCommands];

  // Check if command starts with any safe command
  return allSafeCommands.some(safeCmd => 
    commandStr.startsWith(safeCmd.toLowerCase())
  );
}

/**
 * Assess the risk level of a command
 */
export function assessRiskLevel(command: string[]): "low" | "medium" | "high" {
  const commandStr = command.join(' ').toLowerCase();

  // High risk commands
  if (DANGEROUS_COMMANDS.some(dangerousCmd => 
    commandStr.includes(dangerousCmd.toLowerCase())
  )) {
    return "high";
  }

  // Medium risk commands (write operations)
  if (WRITE_COMMANDS.some(writeCmd => 
    commandStr.startsWith(writeCmd.toLowerCase())
  )) {
    return "medium";
  }

  // Check for potentially dangerous patterns
  if (commandStr.includes('sudo') || 
      commandStr.includes('--force') || 
      commandStr.includes('-f') ||
      commandStr.includes('*') ||
      commandStr.includes('..')) {
    return "high";
  }

  // Network operations
  if (commandStr.includes('http') || 
      commandStr.includes('ftp') || 
      commandStr.includes('ssh')) {
    return "medium";
  }

  return "low";
}

/**
 * Create approval request for a command
 */
export function createApprovalRequest(
  command: string[],
  workdir: string,
  description?: string
): ApprovalRequest {
  return {
    command,
    workdir,
    description,
    riskLevel: assessRiskLevel(command)
  };
}

/**
 * Validate command against security policies
 */
export function validateCommand(
  command: string[],
  workdir: string,
  allowedPaths: string[] = []
): { valid: boolean; error?: string } {
  const commandStr = command.join(' ');

  // Check for command injection attempts
  if (commandStr.includes(';') || 
      commandStr.includes('&&') || 
      commandStr.includes('||') || 
      commandStr.includes('|') ||
      commandStr.includes('`') ||
      commandStr.includes('$(')) {
    return {
      valid: false,
      error: 'Command contains potentially dangerous operators'
    };
  }

  // Check for path traversal attempts
  if (commandStr.includes('../') || commandStr.includes('..\\')) {
    return {
      valid: false,
      error: 'Command contains path traversal attempts'
    };
  }

  // Validate working directory
  if (!workdir || workdir.includes('..')) {
    return {
      valid: false,
      error: 'Invalid working directory'
    };
  }

  // Check if command tries to access restricted paths
  const restrictedPaths = ['/etc', '/sys', '/proc', 'C:\\Windows', 'C:\\System32'];
  if (restrictedPaths.some(path => commandStr.includes(path))) {
    return {
      valid: false,
      error: 'Command attempts to access restricted system paths'
    };
  }

  return { valid: true };
}

/**
 * Format command for display in approval prompt
 */
export function formatCommandForDisplay(command: string[], workdir: string): string {
  const commandStr = command.join(' ');
  return `${workdir}$ ${commandStr}`;
}

/**
 * Get approval prompt message
 */
export function getApprovalPromptMessage(request: ApprovalRequest): string {
  const { command, workdir, riskLevel, description } = request;
  const commandDisplay = formatCommandForDisplay(command, workdir);
  
  let message = `Execute command: ${commandDisplay}`;
  
  if (description) {
    message += `\nDescription: ${description}`;
  }
  
  message += `\nRisk Level: ${riskLevel.toUpperCase()}`;
  
  // Add risk-specific warnings
  switch (riskLevel) {
    case 'high':
      message += '\n⚠️  WARNING: This command may modify system files or have security implications';
      break;
    case 'medium':
      message += '\n⚡ CAUTION: This command will modify files or system state';
      break;
    case 'low':
      message += '\n✓ This appears to be a safe read-only operation';
      break;
  }
  
  return message;
}

/**
 * Get approval options for display
 */
export function getApprovalOptions(): Array<{ key: string; label: string; description: string }> {
  return [
    {
      key: 'y',
      label: 'Yes',
      description: 'Execute this command once'
    },
    {
      key: 'n',
      label: 'No (continue)',
      description: 'Skip this command and continue'
    },
    {
      key: 'x',
      label: 'No (exit)',
      description: 'Skip this command and exit'
    },
    {
      key: 'a',
      label: 'Always',
      description: 'Execute this command and auto-approve similar commands'
    },
    {
      key: 'e',
      label: 'Explain',
      description: 'Get more information about this command'
    }
  ];
}

/**
 * Parse approval response from user input
 */
export function parseApprovalResponse(input: string): ReviewDecision {
  const normalized = input.toLowerCase().trim();
  
  switch (normalized) {
    case 'y':
    case 'yes':
      return ReviewDecision.YES;
    
    case 'n':
    case 'no':
      return ReviewDecision.NO_CONTINUE;
    
    case 'x':
    case 'exit':
      return ReviewDecision.NO_EXIT;
    
    case 'a':
    case 'always':
      return ReviewDecision.ALWAYS;
    
    case 'e':
    case 'explain':
      return ReviewDecision.EXPLAIN;
    
    default:
      return ReviewDecision.NO_CONTINUE;
  }
}

/**
 * Get command explanation for help
 */
export function getCommandExplanation(command: string[]): string {
  const mainCommand = command[0]?.toLowerCase();
  
  const explanations: Record<string, string> = {
    'ls': 'List directory contents',
    'dir': 'List directory contents (Windows)',
    'cat': 'Display file contents',
    'type': 'Display file contents (Windows)',
    'grep': 'Search text patterns in files',
    'find': 'Search for files and directories',
    'git': 'Git version control operations',
    'npm': 'Node.js package manager operations',
    'pip': 'Python package installer operations',
    'rm': 'Remove/delete files or directories',
    'mv': 'Move or rename files',
    'cp': 'Copy files or directories',
    'chmod': 'Change file permissions',
    'sudo': 'Execute commands with elevated privileges',
    'curl': 'Transfer data from/to servers',
    'wget': 'Download files from web',
    'docker': 'Container management operations',
    'kill': 'Terminate processes'
  };
  
  const explanation = explanations[mainCommand];
  if (explanation) {
    return `${mainCommand}: ${explanation}`;
  }
  
  return `${mainCommand}: Command not in explanation database. Please review carefully.`;
}

/**
 * Check if approval policy allows automatic execution
 */
export function shouldRequestApproval(
  command: string[],
  approvalPolicy: ApprovalPolicy,
  additionalSafeCommands: string[] = []
): boolean {
  return !canAutoApprove(command, approvalPolicy, additionalSafeCommands);
}

/**
 * Create security context for command execution
 */
export function createSecurityContext(
  command: string[],
  workdir: string,
  approvalPolicy: ApprovalPolicy
): {
  allowExecution: boolean;
  requiresApproval: boolean;
  riskLevel: "low" | "medium" | "high";
  restrictions: string[];
} {
  const validation = validateCommand(command, workdir);
  const riskLevel = assessRiskLevel(command);
  const requiresApproval = shouldRequestApproval(command, approvalPolicy);
  
  const restrictions: string[] = [];
  
  if (!validation.valid) {
    restrictions.push(validation.error || 'Command validation failed');
  }
  
  if (riskLevel === 'high') {
    restrictions.push('High-risk command requires careful review');
  }
  
  return {
    allowExecution: validation.valid,
    requiresApproval,
    riskLevel,
    restrictions
  };
}
