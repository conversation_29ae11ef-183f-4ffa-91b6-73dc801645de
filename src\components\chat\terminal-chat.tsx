import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Box, Text, useInput } from 'ink';
import { AppConfig, ResponseItem, ApprovalPolicy, OverlayModeType } from '../../types';
import { TerminalChatInput } from './terminal-chat-input';
import { TerminalChatResponseItem } from './terminal-chat-response-item';
import { ModelOverlay } from '../overlays/model-overlay';
import { HelpOverlay } from '../overlays/help-overlay';
import { HistoryOverlay } from '../overlays/history-overlay';
import { ApprovalModeOverlay } from '../overlays/approval-mode-overlay';
import { AgentLoop } from '../../utils/agent/agent-loop';
import { WelcomeMessage, LoadingSpinner, StatusIndicator } from '../../app';

interface TerminalChatProps {
  config: AppConfig;
  initialMessage?: string;
  onExit: () => void;
}

export function TerminalChat({ config, initialMessage, onExit }: TerminalChatProps) {
  const [model, setModel] = useState<string>(config.model);
  const [provider, setProvider] = useState<string>(config.provider);
  const [items, setItems] = useState<ResponseItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [approvalPolicy, setApprovalPolicy] = useState<ApprovalPolicy>(config.approvalMode);
  const [overlayMode, setOverlayMode] = useState<OverlayModeType>("none");
  const [error, setError] = useState<string | null>(null);
  const [showWelcome, setShowWelcome] = useState<boolean>(!initialMessage);

  const agentLoopRef = useRef<AgentLoop | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Initialize agent loop
  useEffect(() => {
    const currentConfig = {
      ...config,
      model,
      provider,
      approvalMode: approvalPolicy
    };

    agentLoopRef.current = new AgentLoop(currentConfig);
  }, [config, model, provider, approvalPolicy]);

  // Handle initial message
  useEffect(() => {
    if (initialMessage && agentLoopRef.current) {
      handleSendMessage(initialMessage);
      setShowWelcome(false);
    }
  }, [initialMessage]);

  // Handle keyboard shortcuts
  useInput((input, key) => {
    if (key.ctrl && input === 'c') {
      handleExit();
    } else if (key.escape) {
      setOverlayMode("none");
    } else if (key.ctrl && input === 'm') {
      setOverlayMode("model");
    } else if (key.ctrl && input === 'h') {
      setOverlayMode("help");
    } else if (key.ctrl && input === 'r') {
      setOverlayMode("history");
    }
  });

  const handleSendMessage = useCallback(async (message: string) => {
    if (!agentLoopRef.current || loading) return;

    // Handle slash commands
    if (message.startsWith('/')) {
      handleSlashCommand(message);
      return;
    }

    setLoading(true);
    setError(null);
    setShowWelcome(false);

    try {
      // Create abort controller for this request
      abortControllerRef.current = new AbortController();

      // Add user message to items
      const userItem: ResponseItem = {
        id: `user-${Date.now()}`,
        type: 'message',
        role: 'user',
        content: [{ type: 'text', text: message }],
        timestamp: Date.now()
      };

      setItems(prev => [...prev, userItem]);

      // Process with agent loop
      const response = await agentLoopRef.current.processMessage(
        message,
        items,
        {
          onProgress: (partialResponse) => {
            // Update items with streaming response
            setItems(prev => {
              const newItems = [...prev];
              const lastItem = newItems[newItems.length - 1];
              
              if (lastItem && lastItem.role === 'assistant') {
                // Update existing assistant response
                lastItem.content = [{ type: 'text', text: partialResponse }];
              } else {
                // Add new assistant response
                newItems.push({
                  id: `assistant-${Date.now()}`,
                  type: 'message',
                  role: 'assistant',
                  content: [{ type: 'text', text: partialResponse }],
                  timestamp: Date.now()
                });
              }
              
              return newItems;
            });
          },
          onApprovalRequired: async (request) => {
            // Handle approval request
            return new Promise((resolve) => {
              // For now, auto-approve based on policy
              // In a full implementation, this would show an approval dialog
              resolve(approvalPolicy !== 'suggest');
            });
          },
          signal: abortControllerRef.current.signal
        }
      );

      // Add final response to items
      if (response) {
        setItems(prev => {
          const newItems = [...prev];
          const lastItem = newItems[newItems.length - 1];
          
          if (lastItem && lastItem.role === 'assistant') {
            // Update existing response with final version
            return [...newItems.slice(0, -1), response];
          } else {
            // Add new response
            return [...newItems, response];
          }
        });
      }
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        // Request was cancelled
        setError('Request cancelled');
      } else {
        setError(err instanceof Error ? err.message : 'Unknown error occurred');
      }
    } finally {
      setLoading(false);
      abortControllerRef.current = null;
    }
  }, [loading, items, approvalPolicy]);

  const handleSlashCommand = (command: string) => {
    const [cmd, ...args] = command.slice(1).split(' ');

    switch (cmd.toLowerCase()) {
      case 'help':
        setOverlayMode("help");
        break;
      
      case 'model':
        setOverlayMode("model");
        break;
      
      case 'history':
        setOverlayMode("history");
        break;
      
      case 'approval':
        setOverlayMode("approval");
        break;
      
      case 'clear':
        setItems([]);
        setShowWelcome(true);
        break;
      
      case 'exit':
        handleExit();
        break;
      
      case 'status':
        showStatus();
        break;
      
      default:
        setError(`Unknown command: /${cmd}`);
    }
  };

  const showStatus = () => {
    const statusItem: ResponseItem = {
      id: `status-${Date.now()}`,
      type: 'message',
      role: 'system',
      content: [{
        type: 'text',
        text: `Status:\n• Model: ${model}\n• Provider: ${provider}\n• Approval: ${approvalPolicy}\n• Messages: ${items.length}`
      }],
      timestamp: Date.now()
    };

    setItems(prev => [...prev, statusItem]);
  };

  const handleExit = () => {
    // Cancel any ongoing requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    onExit();
  };

  const handleModelChange = (newModel: string, newProvider: string) => {
    setModel(newModel);
    setProvider(newProvider);
    setOverlayMode("none");
    
    // Show confirmation
    const confirmationItem: ResponseItem = {
      id: `model-change-${Date.now()}`,
      type: 'message',
      role: 'system',
      content: [{
        type: 'text',
        text: `Switched to ${newModel} (${newProvider})`
      }],
      timestamp: Date.now()
    };

    setItems(prev => [...prev, confirmationItem]);
  };

  const handleApprovalModeChange = (newMode: ApprovalPolicy) => {
    setApprovalPolicy(newMode);
    setOverlayMode("none");
    
    // Show confirmation
    const confirmationItem: ResponseItem = {
      id: `approval-change-${Date.now()}`,
      type: 'message',
      role: 'system',
      content: [{
        type: 'text',
        text: `Approval mode changed to: ${newMode}`
      }],
      timestamp: Date.now()
    };

    setItems(prev => [...prev, confirmationItem]);
  };

  // Render overlay if active
  if (overlayMode !== "none") {
    switch (overlayMode) {
      case "model":
        return (
          <ModelOverlay
            currentModel={model}
            currentProvider={provider}
            onModelChange={handleModelChange}
            onClose={() => setOverlayMode("none")}
          />
        );
      
      case "help":
        return (
          <HelpOverlay
            onClose={() => setOverlayMode("none")}
          />
        );
      
      case "history":
        return (
          <HistoryOverlay
            items={items}
            onClose={() => setOverlayMode("none")}
          />
        );
      
      case "approval":
        return (
          <ApprovalModeOverlay
            currentMode={approvalPolicy}
            onModeChange={handleApprovalModeChange}
            onClose={() => setOverlayMode("none")}
          />
        );
    }
  }

  return (
    <Box flexDirection="column" height="100%">
      {/* Chat messages */}
      <Box flexDirection="column" flexGrow={1} paddingX={1}>
        {showWelcome && <WelcomeMessage config={config} />}
        
        {items.map((item) => (
          <TerminalChatResponseItem
            key={item.id}
            item={item}
          />
        ))}
        
        {loading && (
          <Box marginY={1}>
            <LoadingSpinner text="AI is thinking..." />
          </Box>
        )}
        
        {error && (
          <Box marginY={1}>
            <StatusIndicator status="error" message={error} />
          </Box>
        )}
      </Box>

      {/* Input area */}
      <Box paddingX={1} paddingY={1}>
        <TerminalChatInput
          onSendMessage={handleSendMessage}
          disabled={loading}
          placeholder={loading ? "AI is responding..." : "Type your message..."}
        />
      </Box>
    </Box>
  );
}
