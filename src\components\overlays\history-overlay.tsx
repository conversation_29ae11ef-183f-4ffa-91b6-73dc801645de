import React, { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { ResponseItem } from '../../types';

interface HistoryOverlayProps {
  items: ResponseItem[];
  onClose: () => void;
}

export function HistoryOverlay({ items, onClose }: HistoryOverlayProps) {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [showDetails, setShowDetails] = useState(false);

  // Filter to show only user messages and important system messages
  const displayItems = items.filter(item => 
    item.role === 'user' || 
    (item.role === 'system' && item.type !== 'function_result') ||
    item.type === 'error'
  );

  useInput((input, key) => {
    if (key.escape) {
      if (showDetails) {
        setShowDetails(false);
      } else {
        onClose();
      }
      return;
    }

    if (key.return) {
      setShowDetails(!showDetails);
      return;
    }

    if (key.upArrow && displayItems.length > 0) {
      setSelectedIndex(prev => 
        prev > 0 ? prev - 1 : displayItems.length - 1
      );
      return;
    }

    if (key.downArrow && displayItems.length > 0) {
      setSelectedIndex(prev => 
        prev < displayItems.length - 1 ? prev + 1 : 0
      );
      return;
    }

    if (input === 'c') {
      // Clear history (could be implemented)
      return;
    }
  });

  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  const getItemPreview = (item: ResponseItem) => {
    const textContent = item.content
      .filter(c => c.type === 'text')
      .map(c => c.text)
      .join(' ');
    
    return textContent.length > 80 
      ? textContent.substring(0, 80) + '...'
      : textContent;
  };

  const getRoleIcon = (item: ResponseItem) => {
    switch (item.role) {
      case 'user':
        return '👤';
      case 'assistant':
        return '🤖';
      case 'system':
        return '⚙️';
      case 'tool':
        return '🔧';
      default:
        return '❓';
    }
  };

  const getRoleColor = (item: ResponseItem) => {
    switch (item.role) {
      case 'user':
        return 'cyan';
      case 'assistant':
        return 'green';
      case 'system':
        return 'yellow';
      case 'tool':
        return 'blue';
      default:
        return 'gray';
    }
  };

  if (displayItems.length === 0) {
    return (
      <Box flexDirection="column" height="100%" padding={2}>
        <Box marginBottom={2}>
          <Text color="cyan" bold>
            📜 Conversation History
          </Text>
        </Box>
        
        <Box flex={1} justifyContent="center" alignItems="center">
          <Text color="gray">
            No conversation history yet. Start chatting to see messages here!
          </Text>
        </Box>
        
        <Box marginTop={2}>
          <Text color="gray">
            Press Esc to close
          </Text>
        </Box>
      </Box>
    );
  }

  const selectedItem = displayItems[selectedIndex];

  return (
    <Box flexDirection="column" height="100%" padding={2}>
      {/* Header */}
      <Box marginBottom={2}>
        <Text color="cyan" bold>
          📜 Conversation History ({displayItems.length} items)
        </Text>
      </Box>

      {showDetails && selectedItem ? (
        /* Detail View */
        <Box flexDirection="column" flex={1}>
          <Box marginBottom={2}>
            <Text color={getRoleColor(selectedItem)} bold>
              {getRoleIcon(selectedItem)} {selectedItem.role.charAt(0).toUpperCase() + selectedItem.role.slice(1)}
            </Text>
            <Text color="gray" marginLeft={2}>
              {formatTimestamp(selectedItem.timestamp)}
            </Text>
          </Box>
          
          <Box flexDirection="column" flex={1} paddingX={2} borderStyle="round">
            {selectedItem.content.map((content, index) => {
              switch (content.type) {
                case 'text':
                  return (
                    <Text key={index}>
                      {content.text}
                    </Text>
                  );
                
                case 'function_call':
                  return (
                    <Box key={index} flexDirection="column" marginY={1}>
                      <Text color="blue" bold>
                        Function Call: {content.function_call?.name}
                      </Text>
                      <Text color="gray">
                        {content.function_call?.arguments}
                      </Text>
                    </Box>
                  );
                
                default:
                  return null;
              }
            })}
          </Box>
          
          {selectedItem.metadata && Object.keys(selectedItem.metadata).length > 0 && (
            <Box marginTop={1} paddingX={2}>
              <Text color="gray" bold>Metadata:</Text>
              <Text color="gray">
                {JSON.stringify(selectedItem.metadata, null, 2)}
              </Text>
            </Box>
          )}
        </Box>
      ) : (
        /* List View */
        <Box flexDirection="column" flex={1}>
          {displayItems.map((item, index) => (
            <Box key={item.id} marginBottom={1}>
              <Text color={
                index === selectedIndex ? 'cyan' : getRoleColor(item)
              }>
                {index === selectedIndex ? '▶ ' : '  '}
                {getRoleIcon(item)} {formatTimestamp(item.timestamp)} - {getItemPreview(item)}
              </Text>
            </Box>
          ))}
        </Box>
      )}

      {/* Footer */}
      <Box marginTop={2} flexDirection="column">
        <Text color="gray">
          {showDetails 
            ? 'Press Enter to go back to list, Esc to close'
            : 'Use ↑↓ to navigate, Enter for details, Esc to close'
          }
        </Text>
        {!showDetails && (
          <Text color="gray" dimColor>
            Showing {displayItems.length} conversation items
          </Text>
        )}
      </Box>
    </Box>
  );
}
