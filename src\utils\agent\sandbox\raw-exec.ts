import { spawn } from 'child_process';
import * as path from 'path';
import { ExecInput, ExecResult, AppConfig, SecurityError } from '../../../types';
import { getDefaultShell, getShellArgs, adaptCommand } from '../platform-commands';

/**
 * Execute command without sandboxing (fallback execution)
 * Used when platform-specific sandboxing is unavailable
 */
export async function exec(
  input: ExecInput,
  config: AppConfig,
  signal?: AbortSignal
): Promise<ExecResult> {
  const startTime = Date.now();
  const { command, workdir = config.workdir || process.cwd(), timeout = config.timeout || 30000, env = {} } = input;

  // Validate input
  if (!command || command.length === 0) {
    throw new SecurityError('Empty command not allowed');
  }

  // Adapt command for current platform
  const adaptedCommand = adaptCommand(command);
  
  // Resolve working directory
  const resolvedWorkdir = path.resolve(workdir);
  
  // Validate working directory exists and is accessible
  try {
    const fs = require('fs');
    if (!fs.existsSync(resolvedWorkdir)) {
      throw new SecurityError(`Working directory does not exist: ${resolvedWorkdir}`);
    }
    
    const stats = fs.statSync(resolvedWorkdir);
    if (!stats.isDirectory()) {
      throw new SecurityError(`Working directory is not a directory: ${resolvedWorkdir}`);
    }
  } catch (error) {
    if (error instanceof SecurityError) throw error;
    throw new SecurityError(`Cannot access working directory: ${resolvedWorkdir}`);
  }

  // Prepare environment
  const execEnv = {
    ...process.env,
    ...env,
    // Ensure PATH is available
    PATH: process.env.PATH || '',
    // Set working directory in environment
    PWD: resolvedWorkdir
  };

  // Get shell and command arguments
  const shell = getDefaultShell();
  const commandString = adaptedCommand.join(' ');
  const shellArgs = getShellArgs(commandString);

  return new Promise<ExecResult>((resolve, reject) => {
    let stdout = '';
    let stderr = '';
    let isResolved = false;

    // Create child process
    const child = spawn(shell, shellArgs, {
      cwd: resolvedWorkdir,
      env: execEnv,
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: false, // We're already using shell
      windowsHide: true
    });

    // Handle abort signal
    const abortHandler = () => {
      if (!isResolved) {
        child.kill('SIGTERM');
        setTimeout(() => {
          if (!child.killed) {
            child.kill('SIGKILL');
          }
        }, 5000);
      }
    };

    if (signal) {
      signal.addEventListener('abort', abortHandler);
    }

    // Set timeout
    const timeoutId = setTimeout(() => {
      if (!isResolved) {
        child.kill('SIGTERM');
        setTimeout(() => {
          if (!child.killed) {
            child.kill('SIGKILL');
          }
        }, 5000);
      }
    }, timeout);

    // Collect stdout
    child.stdout?.on('data', (data: Buffer) => {
      stdout += data.toString();
    });

    // Collect stderr
    child.stderr?.on('data', (data: Buffer) => {
      stderr += data.toString();
    });

    // Handle process exit
    child.on('exit', (code, signal) => {
      if (isResolved) return;
      isResolved = true;

      clearTimeout(timeoutId);
      if (signal) {
        signal.removeEventListener('abort', abortHandler);
      }

      const duration = Date.now() - startTime;
      const exitCode = code || 0;

      const result: ExecResult = {
        success: exitCode === 0,
        stdout: stdout.trim(),
        stderr: stderr.trim(),
        exitCode,
        duration,
        command: adaptedCommand,
        workdir: resolvedWorkdir
      };

      resolve(result);
    });

    // Handle process errors
    child.on('error', (error) => {
      if (isResolved) return;
      isResolved = true;

      clearTimeout(timeoutId);
      if (signal) {
        signal.removeEventListener('abort', abortHandler);
      }

      const duration = Date.now() - startTime;

      const result: ExecResult = {
        success: false,
        stdout: stdout.trim(),
        stderr: stderr.trim() || error.message,
        exitCode: 1,
        duration,
        command: adaptedCommand,
        workdir: resolvedWorkdir,
        error: error.message
      };

      resolve(result);
    });

    // Handle spawn errors
    child.on('spawn', () => {
      // Process spawned successfully
    });
  });
}

/**
 * Execute command with basic security checks
 */
export async function execWithBasicSecurity(
  input: ExecInput,
  config: AppConfig,
  additionalWritableRoots: ReadonlyArray<string> = [],
  signal?: AbortSignal
): Promise<ExecResult> {
  // Perform basic security validation
  validateCommandSecurity(input, config, additionalWritableRoots);
  
  // Execute with raw exec
  return exec(input, config, signal);
}

/**
 * Validate command for basic security
 */
function validateCommandSecurity(
  input: ExecInput,
  config: AppConfig,
  additionalWritableRoots: ReadonlyArray<string>
): void {
  const { command, workdir = config.workdir || process.cwd() } = input;
  
  // Check for dangerous command patterns
  const commandString = command.join(' ').toLowerCase();
  
  // Block obvious dangerous patterns
  const dangerousPatterns = [
    'rm -rf /',
    'del /s /q c:\\',
    'format c:',
    'dd if=/dev/zero',
    'sudo rm',
    ':(){ :|:& };:',  // Fork bomb
    'chmod 777',
    'chown root'
  ];
  
  for (const pattern of dangerousPatterns) {
    if (commandString.includes(pattern)) {
      throw new SecurityError(`Dangerous command pattern detected: ${pattern}`);
    }
  }
  
  // Validate working directory is within allowed paths
  const resolvedWorkdir = path.resolve(workdir);
  const allowedRoots = [
    config.workdir || process.cwd(),
    ...additionalWritableRoots
  ].map(root => path.resolve(root));
  
  const isAllowed = allowedRoots.some(root => 
    resolvedWorkdir.startsWith(root)
  );
  
  if (!isAllowed) {
    throw new SecurityError(`Working directory not in allowed paths: ${resolvedWorkdir}`);
  }
  
  // Check for path traversal in command arguments
  for (const arg of command) {
    if (arg.includes('../') || arg.includes('..\\')) {
      // Allow some safe cases
      if (!isSafePathTraversal(arg, resolvedWorkdir, allowedRoots)) {
        throw new SecurityError(`Path traversal detected in argument: ${arg}`);
      }
    }
  }
}

/**
 * Check if path traversal is safe (within allowed roots)
 */
function isSafePathTraversal(
  arg: string,
  workdir: string,
  allowedRoots: string[]
): boolean {
  try {
    // Resolve the path relative to workdir
    const resolvedPath = path.resolve(workdir, arg);
    
    // Check if resolved path is within any allowed root
    return allowedRoots.some(root => 
      resolvedPath.startsWith(root)
    );
  } catch (error) {
    // If path resolution fails, consider it unsafe
    return false;
  }
}

/**
 * Get execution capabilities for raw exec
 */
export function getExecutionCapabilities(): {
  sandboxed: boolean;
  networkRestricted: boolean;
  filesystemRestricted: boolean;
  processRestricted: boolean;
} {
  return {
    sandboxed: false,
    networkRestricted: false,
    filesystemRestricted: false,
    processRestricted: false
  };
}

/**
 * Check if raw execution is available
 */
export function isAvailable(): boolean {
  return true; // Raw execution is always available as fallback
}

/**
 * Get security level description
 */
export function getSecurityLevel(): string {
  return 'Basic security checks only - no sandboxing';
}
