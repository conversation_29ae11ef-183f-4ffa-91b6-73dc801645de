// Test setup file for Jest
import 'jest';

// Mock environment variables for tests
process.env.NODE_ENV = 'test';
process.env.OPENAI_API_KEY = 'test-key';

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeEach(() => {
  // Reset console mocks before each test
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterEach(() => {
  // Restore console methods after each test
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// Global test utilities
global.testUtils = {
  createMockConfig: () => ({
    model: 'gpt-4',
    provider: 'openai',
    approvalMode: 'suggest' as const,
    maxTokens: 4096,
    temperature: 0.7,
    timeout: 30000,
    workdir: process.cwd(),
    additionalWritableRoots: []
  }),
  
  createMockResponseItem: (overrides = {}) => ({
    id: 'test-id',
    type: 'message' as const,
    role: 'assistant' as const,
    content: [{ type: 'text' as const, text: 'Test response' }],
    timestamp: Date.now(),
    ...overrides
  })
};

// Extend global namespace for TypeScript
declare global {
  namespace NodeJS {
    interface Global {
      testUtils: {
        createMockConfig: () => any;
        createMockResponseItem: (overrides?: any) => any;
      };
    }
  }
}
