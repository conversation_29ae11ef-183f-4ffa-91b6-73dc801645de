import React, { useState, useEffect } from 'react';
import { Box, Text, useApp } from 'ink';
import { AppConfig } from './types';
import { TerminalChat } from './components/chat/terminal-chat';
import { isInGitRepository, getGitRepositoryRoot } from './utils/config';
import { getSandboxCapabilities } from './utils/agent/sandbox';

interface AppProps {
  config: AppConfig;
  initialMessage?: string;
  verbose?: boolean;
}

export function App({ config, initialMessage, verbose }: AppProps) {
  const { exit } = useApp();
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [warnings, setWarnings] = useState<string[]>([]);

  useEffect(() => {
    async function initialize() {
      try {
        const newWarnings: string[] = [];

        // Check if we're in a git repository
        if (!isInGitRepository()) {
          newWarnings.push('Not in a git repository - some features may be limited');
        }

        // Check sandbox capabilities
        const sandbox = getSandboxCapabilities();
        if (!sandbox.sandboxed && verbose) {
          newWarnings.push(`Using ${sandbox.name} - limited security isolation`);
        }

        // Validate working directory
        if (config.workdir) {
          const fs = require('fs');
          if (!fs.existsSync(config.workdir)) {
            throw new Error(`Working directory does not exist: ${config.workdir}`);
          }
        }

        setWarnings(newWarnings);
        setIsReady(true);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Initialization failed');
      }
    }

    initialize();
  }, [config, verbose]);

  // Handle app exit
  const handleExit = () => {
    exit();
  };

  if (error) {
    return (
      <Box flexDirection="column" padding={1}>
        <Text color="red">❌ Error: {error}</Text>
        <Text color="gray">Press Ctrl+C to exit</Text>
      </Box>
    );
  }

  if (!isReady) {
    return (
      <Box flexDirection="column" padding={1}>
        <Text color="blue">🚀 Initializing Kritrima AI...</Text>
        <Text color="gray">Loading configuration and checking environment...</Text>
      </Box>
    );
  }

  return (
    <Box flexDirection="column" height="100%">
      {/* Header */}
      <Box flexDirection="column" marginBottom={1}>
        <Text color="cyan" bold>
          🤖 Kritrima AI CLI
        </Text>
        <Text color="gray">
          Model: {config.model} | Provider: {config.provider} | Mode: {config.approvalMode}
        </Text>
        
        {/* Show warnings */}
        {warnings.length > 0 && (
          <Box flexDirection="column" marginTop={1}>
            {warnings.map((warning, index) => (
              <Text key={index} color="yellow">
                ⚠️  {warning}
              </Text>
            ))}
          </Box>
        )}

        {/* Show git info if available */}
        {isInGitRepository() && verbose && (
          <Text color="green">
            📁 Git repository: {getGitRepositoryRoot()}
          </Text>
        )}

        {/* Show sandbox info if verbose */}
        {verbose && (
          <Text color="blue">
            🔒 Security: {getSandboxCapabilities().name}
          </Text>
        )}
      </Box>

      {/* Main chat interface */}
      <Box flex={1}>
        <TerminalChat 
          config={config}
          initialMessage={initialMessage}
          onExit={handleExit}
        />
      </Box>

      {/* Footer */}
      <Box marginTop={1}>
        <Text color="gray" dimColor>
          Press Ctrl+C to exit | Type /help for commands | Type /model to switch models
        </Text>
      </Box>
    </Box>
  );
}

/**
 * Error boundary component for the app
 */
interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error }>;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('React Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;
      return <FallbackComponent error={this.state.error!} />;
    }

    return this.props.children;
  }
}

/**
 * Default error fallback component
 */
function DefaultErrorFallback({ error }: { error: Error }) {
  return (
    <Box flexDirection="column" padding={1}>
      <Text color="red" bold>
        💥 Application Error
      </Text>
      <Text color="red">
        {error.message}
      </Text>
      <Text color="gray" marginTop={1}>
        Please check your configuration and try again.
      </Text>
      <Text color="gray">
        Press Ctrl+C to exit
      </Text>
    </Box>
  );
}

/**
 * App wrapper with error boundary
 */
export function AppWithErrorBoundary(props: AppProps) {
  return (
    <ErrorBoundary>
      <App {...props} />
    </ErrorBoundary>
  );
}

/**
 * Welcome message component
 */
export function WelcomeMessage({ config }: { config: AppConfig }) {
  return (
    <Box flexDirection="column" padding={1}>
      <Text color="cyan" bold>
        Welcome to Kritrima AI! 🚀
      </Text>
      <Text>
        You're connected to {config.provider} using {config.model}
      </Text>
      <Text color="gray" marginTop={1}>
        Available commands:
      </Text>
      <Text color="gray">
        • /help - Show help information
      </Text>
      <Text color="gray">
        • /model - Switch AI model or provider
      </Text>
      <Text color="gray">
        • /history - View command history
      </Text>
      <Text color="gray">
        • /clear - Clear conversation
      </Text>
      <Text color="gray">
        • /exit - Exit the application
      </Text>
      <Text color="green" marginTop={1}>
        Start typing your message below...
      </Text>
    </Box>
  );
}

/**
 * Loading component
 */
export function LoadingSpinner({ text = "Loading..." }: { text?: string }) {
  const [frame, setFrame] = useState(0);
  const frames = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];

  useEffect(() => {
    const interval = setInterval(() => {
      setFrame(prev => (prev + 1) % frames.length);
    }, 100);

    return () => clearInterval(interval);
  }, []);

  return (
    <Box>
      <Text color="blue">
        {frames[frame]} {text}
      </Text>
    </Box>
  );
}

/**
 * Status indicator component
 */
export function StatusIndicator({ 
  status, 
  message 
}: { 
  status: 'success' | 'error' | 'warning' | 'info';
  message: string;
}) {
  const icons = {
    success: '✅',
    error: '❌',
    warning: '⚠️',
    info: 'ℹ️'
  };

  const colors = {
    success: 'green',
    error: 'red',
    warning: 'yellow',
    info: 'blue'
  } as const;

  return (
    <Text color={colors[status]}>
      {icons[status]} {message}
    </Text>
  );
}
