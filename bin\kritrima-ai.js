#!/usr/bin/env node

const path = require('path');
const fs = require('fs');

// Check if we're running from source or built
const srcPath = path.join(__dirname, '..', 'src', 'cli.tsx');
const distPath = path.join(__dirname, '..', 'dist', 'cli.js');

if (fs.existsSync(distPath)) {
  // Running from built version
  require(distPath);
} else if (fs.existsSync(srcPath)) {
  // Running from source with tsx
  const { spawn } = require('child_process');
  const tsxPath = path.join(__dirname, '..', 'node_modules', '.bin', 'tsx');
  
  if (fs.existsSync(tsxPath)) {
    const child = spawn(tsxPath, [srcPath, ...process.argv.slice(2)], {
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });
    
    child.on('exit', (code) => {
      process.exit(code || 0);
    });
  } else {
    console.error('Error: tsx not found. Please run "npm install" first.');
    process.exit(1);
  }
} else {
  console.error('Error: Neither built version nor source files found.');
  console.error('Please run "npm run build" or ensure source files exist.');
  process.exit(1);
}
