#!/usr/bin/env node

import React from 'react';
import { render } from 'ink';
import { Command } from 'commander';
import { App } from './app';
import { loadConfig, getApiKey } from './utils/config';
import { AppConfig, ConfigError } from './types';
import { testConnection } from './utils/openai-client';
import { getSandboxCapabilities } from './utils/agent/sandbox';

const program = new Command();

program
  .name('kritrima-ai')
  .description('Sophisticated AI-powered command-line interface with multi-provider support')
  .version('1.0.0')
  .option('-m, --model <model>', 'AI model to use')
  .option('-p, --provider <provider>', 'AI provider to use')
  .option('-a, --approval <mode>', 'Approval mode: suggest, auto-edit, full-auto')
  .option('-w, --workdir <path>', 'Working directory')
  .option('-t, --timeout <ms>', 'Command timeout in milliseconds')
  .option('-v, --verbose', 'Verbose output')
  .option('--test-connection', 'Test connection to AI provider')
  .option('--test-sandbox', 'Test sandbox functionality')
  .option('--config', 'Show current configuration')
  .option('--providers', 'List available providers')
  .option('--models [provider]', 'List available models for provider')
  .argument('[message]', 'Initial message to send to AI');

program.parse();

const options = program.opts();
const args = program.args;

async function main() {
  try {
    // Load configuration with command line overrides
    const configOverrides: Partial<AppConfig> = {};
    
    if (options.model) configOverrides.model = options.model;
    if (options.provider) configOverrides.provider = options.provider;
    if (options.approval) configOverrides.approvalMode = options.approval;
    if (options.workdir) configOverrides.workdir = options.workdir;
    if (options.timeout) configOverrides.timeout = parseInt(options.timeout, 10);

    const config = loadConfig(configOverrides);

    // Handle special commands
    if (options.config) {
      await showConfig(config);
      return;
    }

    if (options.providers) {
      await showProviders();
      return;
    }

    if (options.models) {
      await showModels(options.models || config.provider);
      return;
    }

    if (options.testConnection) {
      await testConnectionCommand(config);
      return;
    }

    if (options.testSandbox) {
      await testSandboxCommand(config);
      return;
    }

    // Validate API key
    const apiKey = getApiKey(config.provider);
    if (!apiKey) {
      console.error(`❌ No API key found for provider: ${config.provider}`);
      console.error(`Please set the appropriate environment variable.`);
      console.error(`For ${config.provider}: ${getProviderEnvKey(config.provider)}`);
      process.exit(1);
    }

    // Test connection if verbose
    if (options.verbose) {
      console.log('🔍 Testing connection...');
      const connected = await testConnection(config.provider);
      if (!connected) {
        console.error(`❌ Failed to connect to ${config.provider}`);
        process.exit(1);
      }
      console.log(`✅ Connected to ${config.provider}`);
    }

    // Get initial message from arguments
    const initialMessage = args.join(' ');

    // Render the main application
    const { waitUntilExit } = render(
      <App 
        config={config} 
        initialMessage={initialMessage}
        verbose={options.verbose}
      />
    );

    await waitUntilExit();
  } catch (error) {
    if (error instanceof ConfigError) {
      console.error(`❌ Configuration Error: ${error.message}`);
    } else {
      console.error(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    process.exit(1);
  }
}

/**
 * Show current configuration
 */
async function showConfig(config: AppConfig) {
  console.log('📋 Current Configuration:');
  console.log(`  Model: ${config.model}`);
  console.log(`  Provider: ${config.provider}`);
  console.log(`  Approval Mode: ${config.approvalMode}`);
  console.log(`  Working Directory: ${config.workdir}`);
  console.log(`  Timeout: ${config.timeout}ms`);
  console.log(`  Max Tokens: ${config.maxTokens}`);
  console.log(`  Temperature: ${config.temperature}`);
  
  // Show sandbox info
  const sandbox = getSandboxCapabilities();
  console.log(`\n🔒 Security:`)
  console.log(`  Sandbox: ${sandbox.name}`);
  console.log(`  Security Level: ${sandbox.securityLevel}`);
  console.log(`  Sandboxed: ${sandbox.sandboxed ? '✅' : '❌'}`);
  console.log(`  Network Restricted: ${sandbox.networkRestricted ? '✅' : '❌'}`);
  console.log(`  Filesystem Restricted: ${sandbox.filesystemRestricted ? '✅' : '❌'}`);
}

/**
 * Show available providers
 */
async function showProviders() {
  const { providers } = await import('./utils/providers');
  
  console.log('🌐 Available Providers:');
  for (const [key, provider] of Object.entries(providers)) {
    const apiKey = getApiKey(key);
    const status = apiKey ? '✅' : '❌';
    console.log(`  ${status} ${provider.name} (${key})`);
    console.log(`     Base URL: ${provider.baseURL}`);
    console.log(`     Env Key: ${provider.envKey}`);
    console.log(`     Default Model: ${provider.defaultModel}`);
    console.log('');
  }
}

/**
 * Show available models for a provider
 */
async function showModels(provider: string) {
  try {
    const { fetchModels } = await import('./utils/model-utils');
    const { getProviderModels } = await import('./utils/providers');
    
    console.log(`🤖 Models for ${provider}:`);
    
    try {
      const models = await fetchModels(provider);
      if (models.length > 0) {
        models.forEach(model => console.log(`  • ${model}`));
      } else {
        console.log('  No models found');
      }
    } catch (error) {
      console.log('  Failed to fetch from API, showing predefined models:');
      const fallbackModels = getProviderModels(provider);
      fallbackModels.forEach(model => console.log(`  • ${model}`));
    }
  } catch (error) {
    console.error(`❌ Error fetching models: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Test connection to AI provider
 */
async function testConnectionCommand(config: AppConfig) {
  console.log(`🔍 Testing connection to ${config.provider}...`);
  
  try {
    const connected = await testConnection(config.provider);
    if (connected) {
      console.log(`✅ Successfully connected to ${config.provider}`);
    } else {
      console.log(`❌ Failed to connect to ${config.provider}`);
      process.exit(1);
    }
  } catch (error) {
    console.error(`❌ Connection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    process.exit(1);
  }
}

/**
 * Test sandbox functionality
 */
async function testSandboxCommand(config: AppConfig) {
  console.log('🔒 Testing sandbox functionality...');
  
  try {
    const { testSandbox } = await import('./utils/agent/sandbox');
    const result = await testSandbox(config);
    
    console.log(`Sandbox: ${result.sandbox}`);
    console.log(`Security Level: ${result.capabilities.securityLevel}`);
    console.log(`Overall Status: ${result.success ? '✅ PASSED' : '❌ FAILED'}`);
    console.log('\nTest Results:');
    
    for (const test of result.testResults) {
      const status = test.passed ? '✅' : '❌';
      console.log(`  ${status} ${test.test}: ${test.details || 'OK'}`);
    }
    
    if (!result.success) {
      process.exit(1);
    }
  } catch (error) {
    console.error(`❌ Sandbox test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    process.exit(1);
  }
}

/**
 * Get environment variable key for provider
 */
function getProviderEnvKey(provider: string): string {
  const { getProvider } = require('./utils/providers');
  const providerConfig = getProvider(provider);
  return providerConfig?.envKey || `${provider.toUpperCase()}_API_KEY`;
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error('❌ Unhandled Rejection:', reason);
  process.exit(1);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Goodbye!');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n👋 Goodbye!');
  process.exit(0);
});

// Run the main function
main().catch((error) => {
  console.error('❌ Fatal Error:', error.message);
  process.exit(1);
});
