import * as os from 'os';
import * as path from 'path';

/**
 * Command mappings from Unix to Windows
 */
const UNIX_TO_WINDOWS_COMMANDS: Record<string, string> = {
  'ls': 'dir',
  'cat': 'type',
  'grep': 'findstr',
  'rm': 'del',
  'mv': 'move',
  'cp': 'copy',
  'mkdir': 'md',
  'rmdir': 'rd',
  'pwd': 'cd',
  'which': 'where',
  'ps': 'tasklist',
  'kill': 'taskkill',
  'clear': 'cls',
  'head': 'more',
  'tail': 'more',
  'touch': 'echo.',
  'chmod': 'attrib',
  'df': 'fsutil volume diskfree',
  'du': 'dir /s',
  'find': 'dir /s /b',
  'wc': 'find /c /v ""'
};

/**
 * Command mappings from Windows to Unix
 */
const WINDOWS_TO_UNIX_COMMANDS: Record<string, string> = {
  'dir': 'ls',
  'type': 'cat',
  'findstr': 'grep',
  'del': 'rm',
  'move': 'mv',
  'copy': 'cp',
  'md': 'mkdir',
  'rd': 'rmdir',
  'where': 'which',
  'tasklist': 'ps',
  'taskkill': 'kill',
  'cls': 'clear',
  'more': 'head',
  'attrib': 'chmod'
};

/**
 * Platform-specific argument mappings
 */
const ARGUMENT_MAPPINGS: Record<string, Record<string, string[]>> = {
  'ls': {
    '-l': ['/l'],
    '-a': ['/a'],
    '-la': ['/a', '/l'],
    '-al': ['/a', '/l']
  },
  'dir': {
    '/l': ['-l'],
    '/a': ['-a'],
    '/s': ['-R']
  },
  'grep': {
    '-i': ['/i'],
    '-n': ['/n'],
    '-r': ['/s']
  },
  'findstr': {
    '/i': ['-i'],
    '/n': ['-n'],
    '/s': ['-r']
  },
  'rm': {
    '-r': ['/s'],
    '-f': ['/f', '/q'],
    '-rf': ['/s', '/f', '/q']
  },
  'del': {
    '/s': ['-r'],
    '/f': ['-f'],
    '/q': ['-f']
  }
};

/**
 * Get current platform
 */
export function getCurrentPlatform(): 'windows' | 'unix' {
  return os.platform() === 'win32' ? 'windows' : 'unix';
}

/**
 * Adapt command for current platform
 */
export function adaptCommand(command: string[]): string[] {
  if (command.length === 0) return command;

  const platform = getCurrentPlatform();
  const [cmd, ...args] = command;
  const lowerCmd = cmd.toLowerCase();

  // If already correct for platform, return as-is
  if (platform === 'windows' && isWindowsCommand(lowerCmd)) {
    return command;
  }
  if (platform === 'unix' && isUnixCommand(lowerCmd)) {
    return command;
  }

  // Adapt command for target platform
  if (platform === 'windows') {
    return adaptToWindows(command);
  } else {
    return adaptToUnix(command);
  }
}

/**
 * Adapt Unix command to Windows
 */
function adaptToWindows(command: string[]): string[] {
  const [cmd, ...args] = command;
  const lowerCmd = cmd.toLowerCase();
  
  // Get Windows equivalent
  const windowsCmd = UNIX_TO_WINDOWS_COMMANDS[lowerCmd];
  if (!windowsCmd) {
    // If no mapping exists, return original command
    return command;
  }

  // Handle special cases
  switch (lowerCmd) {
    case 'ls':
      return adaptLsToDir(args);
    
    case 'cat':
      return adaptCatToType(args);
    
    case 'grep':
      return adaptGrepToFindstr(args);
    
    case 'rm':
      return adaptRmToDel(args);
    
    case 'mv':
      return adaptMvToMove(args);
    
    case 'cp':
      return adaptCpToCopy(args);
    
    case 'touch':
      return adaptTouchToEcho(args);
    
    case 'pwd':
      return ['cd'];
    
    case 'which':
      return ['where', ...args];
    
    default:
      return [windowsCmd, ...args];
  }
}

/**
 * Adapt Windows command to Unix
 */
function adaptToUnix(command: string[]): string[] {
  const [cmd, ...args] = command;
  const lowerCmd = cmd.toLowerCase();
  
  // Get Unix equivalent
  const unixCmd = WINDOWS_TO_UNIX_COMMANDS[lowerCmd];
  if (!unixCmd) {
    return command;
  }

  // Handle special cases
  switch (lowerCmd) {
    case 'dir':
      return adaptDirToLs(args);
    
    case 'type':
      return adaptTypeTocat(args);
    
    case 'findstr':
      return adaptFindstrToGrep(args);
    
    case 'del':
      return adaptDelToRm(args);
    
    case 'move':
      return adaptMoveToMv(args);
    
    case 'copy':
      return adaptCopyToCp(args);
    
    default:
      return [unixCmd, ...args];
  }
}

/**
 * Adapt ls to dir
 */
function adaptLsToDir(args: string[]): string[] {
  const dirArgs: string[] = ['dir'];
  
  for (const arg of args) {
    if (arg === '-l') {
      // Long format - no direct equivalent, just use default
      continue;
    } else if (arg === '-a') {
      dirArgs.push('/a');
    } else if (arg === '-la' || arg === '-al') {
      dirArgs.push('/a');
    } else if (arg.startsWith('-')) {
      // Skip unknown flags
      continue;
    } else {
      // Path argument
      dirArgs.push(arg);
    }
  }
  
  return dirArgs;
}

/**
 * Adapt cat to type
 */
function adaptCatToType(args: string[]): string[] {
  // Filter out Unix-specific flags
  const typeArgs = args.filter(arg => !arg.startsWith('-'));
  return ['type', ...typeArgs];
}

/**
 * Adapt grep to findstr
 */
function adaptGrepToFindstr(args: string[]): string[] {
  const findstrArgs: string[] = ['findstr'];
  let pattern = '';
  let files: string[] = [];
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    if (arg === '-i') {
      findstrArgs.push('/i');
    } else if (arg === '-n') {
      findstrArgs.push('/n');
    } else if (arg === '-r') {
      findstrArgs.push('/s');
    } else if (!arg.startsWith('-')) {
      if (!pattern) {
        pattern = arg;
      } else {
        files.push(arg);
      }
    }
  }
  
  if (pattern) {
    findstrArgs.push(pattern);
  }
  
  findstrArgs.push(...files);
  return findstrArgs;
}

/**
 * Adapt rm to del
 */
function adaptRmToDel(args: string[]): string[] {
  const delArgs: string[] = ['del'];
  
  for (const arg of args) {
    if (arg === '-r' || arg === '-R') {
      delArgs.push('/s');
    } else if (arg === '-f') {
      delArgs.push('/f', '/q');
    } else if (arg === '-rf' || arg === '-Rf') {
      delArgs.push('/s', '/f', '/q');
    } else if (!arg.startsWith('-')) {
      delArgs.push(arg);
    }
  }
  
  return delArgs;
}

/**
 * Adapt mv to move
 */
function adaptMvToMove(args: string[]): string[] {
  // Move command is similar, just filter flags
  const moveArgs = args.filter(arg => !arg.startsWith('-'));
  return ['move', ...moveArgs];
}

/**
 * Adapt cp to copy
 */
function adaptCpToCopy(args: string[]): string[] {
  const copyArgs: string[] = ['copy'];
  
  for (const arg of args) {
    if (arg === '-r' || arg === '-R') {
      // Recursive copy - use xcopy instead
      copyArgs[0] = 'xcopy';
      copyArgs.push('/e');
    } else if (!arg.startsWith('-')) {
      copyArgs.push(arg);
    }
  }
  
  return copyArgs;
}

/**
 * Adapt touch to echo
 */
function adaptTouchToEcho(args: string[]): string[] {
  if (args.length === 0) return ['echo.'];
  
  // Create empty file using echo
  const filename = args[args.length - 1];
  return ['echo.', '>', filename];
}

/**
 * Adapt dir to ls (reverse direction)
 */
function adaptDirToLs(args: string[]): string[] {
  const lsArgs: string[] = ['ls'];
  
  for (const arg of args) {
    if (arg === '/a') {
      lsArgs.push('-a');
    } else if (arg === '/l') {
      lsArgs.push('-l');
    } else if (arg === '/s') {
      lsArgs.push('-R');
    } else if (!arg.startsWith('/')) {
      lsArgs.push(arg);
    }
  }
  
  return lsArgs;
}

/**
 * Adapt type to cat
 */
function adaptTypeTocat(args: string[]): string[] {
  return ['cat', ...args];
}

/**
 * Adapt findstr to grep
 */
function adaptFindstrToGrep(args: string[]): string[] {
  const grepArgs: string[] = ['grep'];
  
  for (const arg of args) {
    if (arg === '/i') {
      grepArgs.push('-i');
    } else if (arg === '/n') {
      grepArgs.push('-n');
    } else if (arg === '/s') {
      grepArgs.push('-r');
    } else if (!arg.startsWith('/')) {
      grepArgs.push(arg);
    }
  }
  
  return grepArgs;
}

/**
 * Adapt del to rm
 */
function adaptDelToRm(args: string[]): string[] {
  const rmArgs: string[] = ['rm'];
  
  for (const arg of args) {
    if (arg === '/s') {
      rmArgs.push('-r');
    } else if (arg === '/f' || arg === '/q') {
      rmArgs.push('-f');
    } else if (!arg.startsWith('/')) {
      rmArgs.push(arg);
    }
  }
  
  return rmArgs;
}

/**
 * Adapt move to mv
 */
function adaptMoveToMv(args: string[]): string[] {
  return ['mv', ...args];
}

/**
 * Adapt copy to cp
 */
function adaptCopyToCp(args: string[]): string[] {
  return ['cp', ...args];
}

/**
 * Check if command is a Windows command
 */
function isWindowsCommand(cmd: string): boolean {
  return Object.values(UNIX_TO_WINDOWS_COMMANDS).includes(cmd) ||
         ['cmd', 'powershell', 'dir', 'type', 'findstr', 'del', 'move', 'copy'].includes(cmd);
}

/**
 * Check if command is a Unix command
 */
function isUnixCommand(cmd: string): boolean {
  return Object.keys(UNIX_TO_WINDOWS_COMMANDS).includes(cmd) ||
         ['bash', 'sh', 'zsh', 'ls', 'cat', 'grep', 'rm', 'mv', 'cp'].includes(cmd);
}

/**
 * Get shell for current platform
 */
export function getDefaultShell(): string {
  const platform = getCurrentPlatform();
  
  if (platform === 'windows') {
    return process.env.COMSPEC || 'cmd.exe';
  } else {
    return process.env.SHELL || '/bin/bash';
  }
}

/**
 * Get shell arguments for command execution
 */
export function getShellArgs(command: string): string[] {
  const platform = getCurrentPlatform();
  
  if (platform === 'windows') {
    return ['/c', command];
  } else {
    return ['-c', command];
  }
}
